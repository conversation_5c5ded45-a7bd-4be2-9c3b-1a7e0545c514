{"solidity.monoRepoSupport": false, "solidity.enabledAsYouTypeCompilationErrorCheck": true, "solidity.defaultCompiler": "remote", "solidity.formatter": "forge", "solidity.compileUsingRemoteVersion": "v0.8.22+commit.4fc1097e", "solidity.packageDefaultDependenciesDirectory": "lib", "solidity.packageDefaultDependenciesContractsDirectory": "contracts", "solidity.remappings": [], "solidity.linter": "solhint"}