// SPDX-License-Identifier: UNLICENSED
pragma solidity 0.8.22;

// constants that are used in scripts
library Const {
    address internal constant VIRTUAL_ASSET = address(type(uint160).max); // 0xff..ff

    // ----- Arbitrum -----
    uint internal constant ArbiMain_chainId = 42_161;
    uint internal constant ArbiSep_chainId = 421_614;

    // accounts
    address internal constant ArbiMain_owner = ******************************************; // multisig
    address internal constant ArbiMain_deployerAcc = ******************************************; // main-deployer
    address internal constant ArbiMain_feeRecipient = ******************************************; // collarprotocol.eth
    address internal constant ArbiSep_owner = ******************************************;
    address internal constant ArbiSep_deployerAcc = ******************************************;
    address internal constant ArbiSep_feeRecipient = ******************************************;

    // uniswap
    address internal constant ArbiMain_UniRouter = ******************************************;
    address internal constant ArbiSep_UniRouter = ******************************************;
    address internal constant ArbiSep_UniFactory = ******************************************;
    address internal constant ArbiSep_UniPosMan = ******************************************;

    // CL feeds
    address internal constant ArbiMain_SeqFeed = ******************************************;
    address internal constant ArbiMain_CLFeedETH_USD = ******************************************;
    address internal constant ArbiMain_CLFeedWBTC_USD = ******************************************;
    address internal constant ArbiMain_CLFeedUSDC_USD = ******************************************;
    address internal constant ArbiMain_CLFeedUSDT_USD = ******************************************;
    // Note: CL feeds and some Sepolia assets are not used in scripts, but are used in tests
    // so are not defined here to reduce clutter.

    // assets
    address internal constant ArbiMain_USDC = ******************************************;
    address internal constant ArbiMain_USDT = ******************************************;
    address internal constant ArbiMain_WETH = ******************************************;
    address internal constant ArbiMain_WBTC = ******************************************;
    // CollarOwnedERC20 deployed on 12/11/2024
    address internal constant ArbiSep_tUSDC = ******************************************;
    address internal constant ArbiSep_tWETH = ******************************************;
    address internal constant ArbiSep_tWBTC = ******************************************;

    // artifacts
    string internal constant ArbiMain_artifactsName = "arbitrum_mainnet_collar_protocol_deployment";
    string internal constant ArbiSep_artifactsName = "arbitrum_sepolia_collar_protocol_deployment";

    // ----- Base -----
    uint internal constant OPBaseMain_chainId = 8453;
    uint internal constant OPBaseSep_chainId = 84_532;

    // accounts
    address internal constant OPBaseMain_owner = ******************************************; // multisig
    address internal constant OPBaseMain_deployerAcc = ******************************************; // main-deployer
    address internal constant OPBaseMain_feeRecipient = ******************************************; // collarprotocol.eth
    address internal constant OPBaseSep_owner = ******************************************; // base sep safe multisig
    address internal constant OPBaseSep_deployerAcc = ******************************************;
    address internal constant OPBaseSep_feeRecipient = ******************************************;

    // uniswap
    // https://docs.uniswap.org/contracts/v3/reference/deployments/base-deployments
    address internal constant OPBaseMain_UniRouter = ******************************************;
    address internal constant OPBaseSep_UniRouter = ******************************************;
    address internal constant OPBaseSep_UniFactory = ******************************************;
    address internal constant OPBaseSep_UniPosMan = ******************************************;

    // CL feeds
    // https://docs.chain.link/data-feeds/l2-sequencer-feeds#overview
    address internal constant OPBaseMain_SeqFeed = ******************************************;
    // https://docs.chain.link/data-feeds/price-feeds/addresses?network=base&page=1
    address internal constant OPBaseMain_CLFeedETH_USD = ******************************************; // 0.15%, 1200, 8
    address internal constant OPBaseMain_CLFeedUSDC_USD = ******************************************; // 0.3%, 86400, 8
    address internal constant OPBaseMain_CLFeedCBBTC_USD = ******************************************; // 0.3%, 1200, 8
    address internal constant OPBaseSep_CLFeedETH_USD = ******************************************; // 0.15%, 1200, 8
    address internal constant OPBaseSep_CLFeedUSDC_USD = ******************************************; // 0.1%, 86400, 8

    // assets
    // https://app.uniswap.org/explore/pools/base/
    address internal constant OPBaseMain_USDC = ******************************************;
    address internal constant OPBaseMain_WETH = ******************************************;
    address internal constant OPBaseMain_cbBTC = ******************************************;
    address internal constant OPBaseSep_USDC = ******************************************;
    address internal constant OPBaseSep_WETH = ******************************************;
    // CollarOwnedERC20 deployed on 11/02/2025
    address internal constant OPBaseSep_tUSDC = ******************************************;
    address internal constant OPBaseSep_tWETH = ******************************************;
    address internal constant OPBaseSep_tWBTC = ******************************************;

    // artifacts
    string internal constant OPBaseMain_artifactsName = "opbase_mainnet_collar_protocol_deployment";
    string internal constant OPBaseSep_artifactsName = "opbase_sepolia_collar_protocol_deployment";
}
