[submodule "lib/forge-std"]
	path = lib/forge-std
	url = https://github.com/foundry-rs/forge-std

[submodule "lib/openzeppelin-contracts"]
	path = lib/openzeppelin-contracts
	url = https://github.com/OpenZeppelin/openzeppelin-contracts
[submodule "lib/v3-periphery"]
	path = lib/v3-periphery
	url = https://github.com/uniswap/v3-periphery
[submodule "lib/v3-core"]
	path = lib/v3-core
	url = https://github.com/uniswap/v3-core
[submodule "lib/swap-router-contracts"]
	path = lib/swap-router-contracts
	url = https://github.com/uniswap/swap-router-contracts
